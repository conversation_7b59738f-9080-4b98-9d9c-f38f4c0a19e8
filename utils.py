#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
提供通用的工具函数，包括日志配置、文件操作等
"""

import logging
import os
import sys
from typing import Dict, List, Optional


def setup_logging(level: str = "INFO") -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
    
    Returns:
        配置好的logger对象
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('language_converter.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def ensure_directory_exists(directory: str) -> None:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        

def validate_file_exists(file_path: str) -> bool:
    """
    验证文件是否存在
    
    Args:
        file_path: 文件路径
    
    Returns:
        文件是否存在
    """
    return os.path.isfile(file_path)


def get_values_directories(base_path: str) -> List[str]:
    """
    获取所有values开头的目录
    
    Args:
        base_path: 基础路径
    
    Returns:
        values目录列表
    """
    directories = []
    if not os.path.exists(base_path):
        return directories
        
    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path) and item.startswith('values'):
            directories.append(item)
    
    return sorted(directories)


def unescape_xml_entities(text: str) -> str:
    """
    反转义XML实体
    
    Args:
        text: 包含XML实体的文本
    
    Returns:
        反转义后的文本
    """
    if not text:
        return text
        
    # XML实体映射
    entities = {
        '&lt;': '<',
        '&gt;': '>',
        '&amp;': '&',
        '&quot;': '"',
        '&apos;': "'",
        '&#39;': "'",
        '&#34;': '"',
    }
    
    result = text
    for entity, char in entities.items():
        result = result.replace(entity, char)
    
    return result


def escape_for_json(text: str) -> str:
    """
    为JSON输出转义特殊字符
    
    Args:
        text: 原始文本
    
    Returns:
        转义后的文本
    """
    if not text:
        return text
        
    # 只转义必要的JSON特殊字符
    result = text.replace('\\', '\\\\')  # 反斜杠
    result = result.replace('"', '\\"')   # 双引号
    result = result.replace('\n', '\\n')  # 换行符
    result = result.replace('\r', '\\r')  # 回车符
    result = result.replace('\t', '\\t')  # 制表符
    
    return result
