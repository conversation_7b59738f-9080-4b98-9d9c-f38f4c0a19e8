#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件解析模块
负责读取和解析language_config.json文件，建立语言映射关系
"""

import json
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from utils import validate_file_exists


@dataclass
class LanguageConfig:
    """语言配置数据类"""
    des: str          # 语言描述，如"英文"、"西班牙语"
    key_col: str      # 语言列标识，如"en"、"es"
    gp_code: str      # Google Play代码
    key_string: str   # 字符串键标识，通常是"textKey"
    file_dir: str     # 对应的文件夹名，如"values"、"values-es"


class ConfigParser:
    """配置文件解析器"""
    
    def __init__(self, config_file_path: str):
        """
        初始化配置解析器
        
        Args:
            config_file_path: 配置文件路径
        """
        self.config_file_path = config_file_path
        self.logger = logging.getLogger(__name__)
        self.language_configs: Dict[str, LanguageConfig] = {}
        self.file_dir_to_config: Dict[str, LanguageConfig] = {}
        
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            是否加载成功
        """
        try:
            if not validate_file_exists(self.config_file_path):
                self.logger.error(f"配置文件不存在: {self.config_file_path}")
                return False
            
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if 'languageList' not in config_data:
                self.logger.error("配置文件格式错误：缺少languageList字段")
                return False
            
            # 解析语言配置
            for lang_data in config_data['languageList']:
                try:
                    config = LanguageConfig(
                        des=lang_data.get('des', ''),
                        key_col=lang_data.get('keyCol', ''),
                        gp_code=lang_data.get('gpCode', ''),
                        key_string=lang_data.get('keyString', 'textKey'),
                        file_dir=lang_data.get('fileDir', '')
                    )
                    
                    # 建立映射关系
                    self.language_configs[config.key_col] = config
                    self.file_dir_to_config[config.file_dir] = config
                    
                except KeyError as e:
                    self.logger.warning(f"语言配置缺少必要字段: {e}, 数据: {lang_data}")
                    continue
            
            self.logger.info(f"成功加载 {len(self.language_configs)} 个语言配置")
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件JSON格式错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"加载配置文件时发生错误: {e}")
            return False
    
    def get_config_by_file_dir(self, file_dir: str) -> Optional[LanguageConfig]:
        """
        根据文件夹名获取语言配置
        
        Args:
            file_dir: 文件夹名，如"values"、"values-es"
        
        Returns:
            语言配置对象，如果不存在返回None
        """
        return self.file_dir_to_config.get(file_dir)
    
    def get_config_by_key_col(self, key_col: str) -> Optional[LanguageConfig]:
        """
        根据语言代码获取语言配置
        
        Args:
            key_col: 语言代码，如"en"、"es"
        
        Returns:
            语言配置对象，如果不存在返回None
        """
        return self.language_configs.get(key_col)
    
    def get_all_configs(self) -> List[LanguageConfig]:
        """
        获取所有语言配置
        
        Returns:
            语言配置列表
        """
        return list(self.language_configs.values())
    
    def get_supported_file_dirs(self) -> List[str]:
        """
        获取所有支持的文件夹名
        
        Returns:
            文件夹名列表
        """
        return list(self.file_dir_to_config.keys())
    
    def get_language_order(self) -> List[str]:
        """
        获取语言的显示顺序（按配置文件中的顺序）
        
        Returns:
            语言代码列表，按配置文件顺序排列
        """
        try:
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            order = []
            for lang_data in config_data.get('languageList', []):
                key_col = lang_data.get('keyCol')
                if key_col and key_col in self.language_configs:
                    order.append(key_col)
            
            return order
        except Exception as e:
            self.logger.warning(f"获取语言顺序时发生错误: {e}")
            return list(self.language_configs.keys())
