{"agreement": "Privacy Policy", "analysis_button": "GO", "analysis_title": "Health Analysis", "articles_title": "Health Articles", "blood_pressure_analysis": "Blood Pressure Analysis", "blood_pressure_range1": "SYS < 90 or DIA < 60", "blood_pressure_range2": "SYS 90-119 and DlA 60-79", "blood_pressure_range3": "SYS 90-119 and DlA 60-79", "blood_pressure_range4": "SYS 130-139 or DIA 80-89", "blood_pressure_range5": "SYS 140-180 0r DIA 90-120", "blood_pressure_range6": "SYS>180 0r DIA>120", "brvah_in_loading": "Loading...", "diastolic": "Diastolic", "end_botton": "Get Started", "evaluate_content1": "Sorry for any inconvenience caused", "evaluate_content2": "We're glad you love Daily HeartTrack Launcher！ Thank you！", "evaluate_msg": "How do you evaluate us?", "evaluate_title": "Evaluate", "faq": "FAQ", "first_to_splash": "This action may contain advertising", "go_to_set": "Go to set", "guide11_text": "Easily record heart rate, blood pressure, blood sugar, BMI health data.", "guide11_title": "Record your health data", "guide1_text": "Regularly record and keep track of your health.", "guide1_title": "Focus on health", "guide2_text": "Get a clearer understanding of your health status and changes through charts and graphs.", "guide2_title": "Analyze your health", "guide3_text": "This application is not suitable for use in medical emergencies. If you need any assistance, please consult your doctor.", "guide3_title": "Disclaimer", "health_analysis": "Health Analysis", "health_banner_1": "Health &Dietary \nTips", "health_banner_2": "Daily Health \nNews", "health_rate_analysis": "Heart Rate Analysis", "heart_age": "Age", "heart_ageno": "Unknown", "heart_analysis": "Heart Rate", "heart_average": "Average", "heart_button": "Measure", "heart_gender": "Gender", "heart_gender1": "Male", "heart_gender2": "Female", "heart_gender3": "Other", "heart_history": "History", "heart_max": "Max", "heart_mer_last": "Last measurement", "heart_mer_save": "Save", "heart_mer_tips1": "Cover the camera with your fingers for a while", "heart_mer_tips2": "Please cover the camera with your finger", "heart_mer_title": "Heart Rate", "heart_min": "Min", "heart_more": "More", "heart_overview": "Overview", "heart_overview1": "Relaxed state", "heart_overview2": "Active state", "heart_record": "Save Record", "heart_record_age": "Age:", "heart_record_gender": "Gender:", "heart_record_status": "Status:", "heart_result1": "Slow", "heart_result2": "Normal", "heart_result3": "Fast", "heart_result4": "Warm up", "heart_result5": "Target HR training", "heart_result6": "High intensity", "heart_result7": "Extreme", "heart_result_tips": "The result of your heart rate level is", "heart_result_title": "Result", "heart_start": "Start", "heart_status": "Status", "heart_status1": "Resting", "heart_status10": "Jumping", "heart_status2": "Sitting", "heart_status3": "Standing", "heart_status4": "Lying", "heart_status5": "Meditation", "heart_status6": "Exercise", "heart_status7": "Walking", "heart_status8": "Running", "heart_status9": "Swimming", "heart_title": "Heart Rate", "heart_total": "Total", "heart_trend": "Trend", "heart_unlock_but": "Unlock", "heart_unlock_title": "Watch an ad to unlock", "help_but": "To measure", "help_text1": "Cover both the phone camera and flash with your palm until the measurement is complete.", "help_text2": "If the measurement fails even after covering it at the same time, we apologize that your device is not compatible with this feature.", "help_title": "How to measure heart rate", "helppage_title": "Heart Rate", "info_tab1": "Heart Rate", "info_tab2": "Blood Pressure", "info_tab3": "Blood Sugar", "language_1": "English", "language_10": "Français", "language_11": "日本", "language_12": "Filipino", "language_2": "हिंदी", "language_3": "Português", "language_4": "Русский", "language_5": "bahasa Indonesia", "language_6": "español", "language_7": "한국인", "language_8": "De<PERSON>ch", "language_9": "Italiano", "language_set": "Languages", "language_title": "Languages", "loading_failed": "Loading failed, please check the network and try again", "next_botton": "Next", "permission_txt1": "Authorize overlay permission to", "permission_txt2": "better remind you to record heart rate and protect your health.", "prem_but1": "Cancel", "prem_but2": "Allow", "prem_tips": "In order to achieve heart rate measurement, please allow us to access the camera, which will not result in your privacy being compromised. Please use it with confidence!", "prem_title": "Heart Rate", "pressure_analysis": "Blood Sugar", "pressure_button": "Add", "pressure_date": "Date", "pressure_more": "More", "pressure_overview": "Overview", "pressure_result1": "Hypotension", "pressure_result2": "Normal", "pressure_result3": "Elevated", "pressure_result4": "Hypertension·Stage 1", "pressure_result5": "Hypertension·Stage 2", "pressure_result6": "Hypertensive", "pressure_result_tips": "The result of your blood pressure level is", "pressure_result_title": "Result", "pressure_save": "Save", "pressure_title": "Blood Pressure", "pressure_trend": "Trend", "pressure_unlock_but": "Unlock", "pressure_unlock_title": "Watch an ad to unlock", "privacy_policy": "User Terms", "pulse": "Pulse", "push_blood_btn": "Record", "push_blood_content": "Continuously recording blood pressure data for 7 days can help detect health problems in a timely manner!", "push_btn_big_check": "Check out Now", "push_btn_check": "Check out", "push_btn_full_view": "Read More", "push_btn_measure": "Measure", "push_btn_view": "View", "push_heart_fail": "Suspected abnormal heart rate detected, go and measure your heart rate quickly!", "push_txt_analyze": "Only one measurement value is difficult to better analyze your health. It is recommended that you record more times to better detect changes in your health.", "push_txt_analyze1": "Only one measurement value is difficult to better analyze your health.", "push_txt_analyze2": "It is recommended that you record more times to better detect changes in your health.", "push_txt_eat": "What's good to eat today? Take a look at healthy eating tips as a reference!", "push_txt_exercise": "Monitor heart rate during exercise to avoid danger!", "push_txt_lose_weight": "The formula for calculating weight loss heart rate is \"(220-age) x 0.6 to (220-age) x 0.8\". Have you reached your weight loss heart rate?", "push_txt_morning": "Waking up is an ideal time to measure heart rate, which helps to understand the body's heart rate in a completely relaxed state.", "push_txt_morning1": "Waking up is an ideal time to measure heart rate,", "push_txt_morning2": "Which helps to understand the body's heart rate in a completely relaxed state.", "push_txt_news": "Daily health news has been updated! To learn about the latest medical and health information.", "push_txt_night": "Measuring heart rate before resting at night can help understand the changes in heart rate after the end of the day's activities.", "push_txt_night1": "Measuring heart rate before resting at night", "push_txt_night2": "Can help understand the changes in heart rate after the end of the day's activities.", "push_txt_short_play": "The popular short dramas have been updated!", "push_txt_weather_morning": "Good morning!Click to check weather ...", "push_txt_weather_night": "Good evening!Click to check weather...", "push_txt_weather_storm1": "There is a possibility of a storm happening near...", "push_txt_weather_storm2": "Tropical storms may occur in the near future. click ...", "rate_commit": "<PERSON><PERSON><PERSON>", "rate_commit_5": "Evaluate us", "rate_commit_feedback": "Feedback success", "rate_hint": "Write down your displeasure – we’ll address it immediately.", "record_but": "Add", "record_tips": "Record your first data now！", "reset_homescreen": "After installing Launcher, your home screen will change, making it easier for you to access the features. The home screen will display commonly used apps such as phone, SMS, camera, etc. If you want to place other apps on the home screen, simply swipe up on the home screen to view all apps, and drag the apps you want to the home screen. If you are not satisfied with the new home screen, you can switch back to the original home screen, but please note that when you are no longer using Launcher, you will no longer be able to use the features. Click the button below to change the default launcher.", "set_help": "How to measure heart rate", "set_title": "Settings", "skip": "<PERSON><PERSON>", "sugar_date": "Date", "sugar_more": "More", "sugar_overview": "Overview", "sugar_result1": "Low", "sugar_result2": "Normal", "sugar_result3": "Pre-diabetes", "sugar_result4": "Diabetes", "sugar_result_tips": "The result of your blood sugar level is", "sugar_result_title": "Result", "sugar_save": "Save", "sugar_status": "Status", "sugar_status1": "<PERSON><PERSON><PERSON>", "sugar_status2": "Before exercise", "sugar_status3": "Before a meal", "sugar_status4": "Fasting", "sugar_status5": "After a meal（1h）", "sugar_status6": "After a meal（2h）", "sugar_status7": "After exercise", "sugar_status8": "<PERSON><PERSON><PERSON>", "sugar_title": "Blood Sugar", "sugar_trend": "Trend", "sugar_unlock_but": "Unlock", "sugar_unlock_title": "Watch an ad to unlock", "systolic": "Systolic", "tab1": "Home", "tab2": "Analysis", "tab3": "Health info", "tab4": "Settings", "terms": "Terms of Service", "tracker": "Tracker", "try_again": "Please try again", "unlock_but": "Unlock", "unlock_tips": "Watch an ad to unlock"}