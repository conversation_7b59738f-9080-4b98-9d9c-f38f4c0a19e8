{"agreement": "<PERSON><PERSON><PERSON><PERSON>", "analysis_button": "<PERSON><PERSON>", "analysis_title": "<PERSON><PERSON><PERSON>", "articles_title": "<PERSON><PERSON><PERSON>", "blood_pressure_analysis": "<PERSON><PERSON><PERSON>", "blood_pressure_range1": "SYS < 90 atau DIA < 60", "blood_pressure_range2": "SYS 90-119 dan <PERSON> 60-79", "blood_pressure_range3": "SYS 90-119 dan <PERSON> 60-79", "blood_pressure_range4": "SYS 130-139 atau DIA 80-89", "blood_pressure_range5": "SYS 140-180 atau DIA 90-120", "blood_pressure_range6": "SYS>180 atau DIA>120", "brvah_in_loading": "Memuat...", "diastolic": "Diastolik", "end_botton": "<PERSON><PERSON>", "evaluate_content1": "Maaf atas ketidaknyamanan yang ditimbulkan", "evaluate_content2": "<PERSON><PERSON> senang <PERSON>a menyukai Daily HeartTrack Launcher! Terima kasih!", "evaluate_msg": "Bagaimana Anda mengevaluasi kami?", "evaluate_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_to_splash": "Tindakan ini mungkin mengandung iklan", "go_to_set": "<PERSON><PERSON>", "guide11_text": "<PERSON>gan mudah mere<PERSON>m detak j<PERSON>, te<PERSON><PERSON> darah, kadar gula darah, data kesehatan BMI.", "guide11_title": "Rekam Data Kesehat<PERSON>", "guide1_text": "<PERSON><PERSON><PERSON> rekam dan pantau k<PERSON>an <PERSON>.", "guide1_title": "<PERSON>okus pada <PERSON>", "guide2_text": "Dapatkan pemahaman yang lebih jelas tentang kondisi kesehatan dan perubahan melalui bagan dan grafik.", "guide2_title": "<PERSON><PERSON><PERSON>", "guide3_text": "Aplikasi ini tidak cocok untuk digunakan dalam keadaan darurat medis. Jika membutuhkan bantuan, harap konsultasi ke dokter.", "guide3_title": "<PERSON><PERSON><PERSON><PERSON>", "health_analysis": "<PERSON><PERSON><PERSON>", "health_banner_1": "Tips Kesehatan & Nutrisi", "health_banner_2": "<PERSON><PERSON>", "health_rate_analysis": "<PERSON><PERSON><PERSON>", "heart_age": "Usia", "heart_ageno": "Tidak Diketahui", "heart_analysis": "<PERSON><PERSON>", "heart_average": "<PERSON>a-rata", "heart_button": "<PERSON><PERSON><PERSON>", "heart_gender": "<PERSON><PERSON>", "heart_gender1": "Pria", "heart_gender2": "<PERSON><PERSON>", "heart_gender3": "<PERSON><PERSON><PERSON>", "heart_history": "Riwayat", "heart_max": "<PERSON><PERSON>", "heart_mer_last": "Pengu<PERSON><PERSON>", "heart_mer_save": "Simpan", "heart_mer_tips1": "<PERSON><PERSON><PERSON> kamera dengan jari Anda beberapa saat", "heart_mer_tips2": "<PERSON><PERSON> tutupi kamera dengan jari <PERSON>a", "heart_mer_title": "<PERSON><PERSON>", "heart_min": "Min", "heart_more": "<PERSON><PERSON><PERSON>", "heart_overview": "<PERSON><PERSON><PERSON>", "heart_overview1": "<PERSON><PERSON><PERSON>", "heart_overview2": "Kondisi Aktif", "heart_record": "Simpan <PERSON>", "heart_record_age": "Usia:", "heart_record_gender": "<PERSON><PERSON>:", "heart_record_status": "Status:", "heart_result1": "Lambat", "heart_result2": "Normal", "heart_result3": "Cepat", "heart_result4": "<PERSON><PERSON><PERSON><PERSON>", "heart_result5": "Target Latihan HR", "heart_result6": "Intensitas Tinggi", "heart_result7": "Ekstrem", "heart_result_tips": "<PERSON><PERSON> tingkat detak jantung Anda:", "heart_result_title": "<PERSON><PERSON>", "heart_start": "<PERSON><PERSON>", "heart_status": "Status", "heart_status1": "<PERSON><PERSON><PERSON><PERSON>", "heart_status10": "Melompat", "heart_status2": "<PERSON><PERSON><PERSON>", "heart_status3": "<PERSON><PERSON><PERSON>", "heart_status4": "Berbaring", "heart_status5": "<PERSON><PERSON><PERSON>", "heart_status6": "Olahraga", "heart_status7": "Jalan Kaki", "heart_status8": "<PERSON><PERSON>", "heart_status9": "Berenang", "heart_title": "<PERSON><PERSON>", "heart_total": "Total", "heart_trend": "<PERSON>ren", "heart_unlock_but": "<PERSON><PERSON>", "heart_unlock_title": "Tonton iklan untuk membuka", "help_but": "<PERSON><PERSON><PERSON>", "help_text1": "<PERSON><PERSON>p kamera dan flash ponsel dengan telapak tangan hingga pengukuran selesai.", "help_text2": "<PERSON><PERSON> pengukuran gagal meski sudah ditutup, perangkat <PERSON>a mungkin tidak kompatibel dengan fitur ini.", "help_title": "<PERSON>", "helppage_title": "<PERSON><PERSON>", "info_tab1": "<PERSON><PERSON>", "info_tab2": "<PERSON><PERSON><PERSON>", "info_tab3": "<PERSON><PERSON>", "language_1": "<PERSON><PERSON><PERSON>", "language_10": "<PERSON><PERSON><PERSON>", "language_11": "<PERSON><PERSON><PERSON>", "language_12": "Filipina", "language_2": "Hindi", "language_3": "Portugis", "language_4": "Rusia", "language_5": "Indonesia", "language_6": "Spanyol", "language_7": "Korea", "language_8": "<PERSON><PERSON>", "language_9": "Italia", "language_set": "Bahasa", "language_title": "Bahasa", "loading_failed": "Gagal memuat. Periksa jaringan dan coba lagi.", "next_botton": "Berikutnya", "permission_txt1": "Berikan izin overlay untuk", "permission_txt2": "terima pengingat rekam detak jantung dan jaga kesehatan.", "prem_but1": "<PERSON><PERSON>", "prem_but2": "Izinkan", "prem_tips": "Untuk mengukur detak j<PERSON>ung, berikan akses kamera yang tidak akan melanggar privasi Anda. <PERSON><PERSON>an gunakan dengan percaya diri!", "prem_title": "<PERSON><PERSON>", "pressure_analysis": "<PERSON><PERSON>", "pressure_button": "Tambah", "pressure_date": "Tanggal", "pressure_more": "<PERSON><PERSON><PERSON>", "pressure_overview": "<PERSON><PERSON><PERSON>", "pressure_result1": "Hipotensi", "pressure_result2": "Normal", "pressure_result3": "Tingg<PERSON>", "pressure_result4": "Hipertensi Tahap 1", "pressure_result5": "Hipertensi Tahap 2", "pressure_result6": "Hipertensi Krisis", "pressure_result_tips": "<PERSON><PERSON> tingkat tekanan darah <PERSON>a:", "pressure_result_title": "<PERSON><PERSON>", "pressure_save": "Simpan", "pressure_title": "<PERSON><PERSON><PERSON>", "pressure_trend": "<PERSON>ren", "pressure_unlock_but": "<PERSON><PERSON>", "pressure_unlock_title": "Tonton iklan untuk membuka", "privacy_policy": "<PERSON><PERSON><PERSON>", "pulse": "Denyut", "push_blood_btn": "Rekam", "push_blood_content": "Rekam data tekanan darah selama 7 hari berturut-turut untuk deteksi masalah kesehatan tepat waktu!", "push_btn_big_check": "Cek <PERSON>", "push_btn_check": "Cek", "push_btn_full_view": "Baca Selengkapnya", "push_btn_measure": "<PERSON><PERSON><PERSON>", "push_btn_view": "Lihat", "push_heart_fail": "Detak jantung abnormal terdeteksi, segera ukur!", "push_txt_analyze": "Satu data pengukuran sulit analisis kesehatan. Rekam lebih banyak untuk pantau perubahan.", "push_txt_analyze1": "Satu data detak jantung sulit untuk analisis menyeluruh.", "push_txt_analyze2": "Direkomendasikan melakukan beberapa pengukuran.", "push_txt_eat": "Mau makan apa hari ini? <PERSON>hat tips nutrisi sehat!", "push_txt_exercise": "Pantau detak jantung saat olahraga untuk hindari risiko!", "push_txt_lose_weight": "Rumus detak jantung turun berat: «(220-usia) x0.6 sampai (220-usia)x0.8». Sudah capai target?", "push_txt_morning": "Waktu ideal ukur detak jantung saat bangun tidur.", "push_txt_morning1": "<PERSON><PERSON><PERSON> bangun tidur ideal untuk ukur detak jantung,", "push_txt_morning2": "bantu pahami kondisi jantung saat rileks.", "push_txt_news": "<PERSON><PERSON> kesehatan harian diperbarui! Dapatkan info medis terkini.", "push_txt_night": "<PERSON><PERSON>r detak jantung sebelum tidur untuk pahami perubahan setelah aktivitas.", "push_txt_night1": "<PERSON><PERSON>r detak jantung sebelum istirahat malam", "push_txt_night2": "<PERSON><PERSON> pahami perubahan detak jantung setelah aktivitas.", "push_txt_short_play": "Drama pendek populer diperbarui!", "push_txt_weather_morning": "Selamat pagi! Klik untuk cek cuaca...", "push_txt_weather_night": "Selamat malam! Klik untuk cek cuaca...", "push_txt_weather_storm1": "Badai petir mungkin terjadi di...", "push_txt_weather_storm2": "Badai tropis mungkin datang. Klik...", "rate_commit": "<PERSON><PERSON><PERSON>", "rate_commit_5": "Men<PERSON>val<PERSON><PERSON> kami", "rate_commit_feedback": "<PERSON><PERSON><PERSON><PERSON><PERSON> umpan balik", "rate_hint": "Tuliskan ketidaksenangan <PERSON> - kami akan segera mengata<PERSON>.", "record_but": "Tambah", "record_tips": "Rekam data pertama Anda sekarang!", "set_help": "<PERSON>", "set_title": "<PERSON><PERSON><PERSON><PERSON>", "skip": "<PERSON><PERSON>", "sugar_date": "Tanggal", "sugar_more": "<PERSON><PERSON><PERSON>", "sugar_overview": "<PERSON><PERSON><PERSON>", "sugar_result1": "Rendah", "sugar_result2": "Normal", "sugar_result3": "Pra-Diabetes", "sugar_result4": "Diabetes", "sugar_result_tips": "<PERSON><PERSON> pen<PERSON>n kadar gula darah:", "sugar_result_title": "<PERSON><PERSON>", "sugar_save": "Simpan", "sugar_status": "Status", "sugar_status1": "<PERSON><PERSON><PERSON>", "sugar_status2": "<PERSON><PERSON><PERSON>", "sugar_status3": "Sebelum Ma<PERSON>", "sugar_status4": "<PERSON><PERSON><PERSON>", "sugar_status5": "<PERSON><PERSON><PERSON> (1 jam)", "sugar_status6": "<PERSON><PERSON><PERSON> (2 jam)", "sugar_status7": "<PERSON><PERSON><PERSON>", "sugar_status8": "<PERSON><PERSON><PERSON>", "sugar_title": "<PERSON><PERSON>", "sugar_trend": "<PERSON>ren", "sugar_unlock_but": "<PERSON><PERSON>", "sugar_unlock_title": "Tonton iklan untuk membuka", "systolic": "Sistolik", "tab1": "Be<PERSON><PERSON>", "tab2": "<PERSON><PERSON><PERSON>", "tab3": "<PERSON><PERSON>", "tab4": "<PERSON><PERSON><PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON>", "tracker": "Pelacak", "try_again": "Silakan coba lagi", "unlock_but": "<PERSON><PERSON>", "unlock_tips": "Tonton iklan untuk membuka"}