#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合处理模块
负责整合所有语言的字符串数据，处理缺失翻译，进行数据验证和清理
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from xml_parser import StringResource
from config_parser import ConfigParser, LanguageConfig


@dataclass
class TranslationData:
    """翻译数据类"""
    string_key: str                           # 字符串键
    translations: Dict[str, str]              # 语言代码到翻译文本的映射
    missing_languages: Set[str]               # 缺失翻译的语言
    source_files: Dict[str, str]              # 语言代码到源文件路径的映射


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config_parser: ConfigParser):
        """
        初始化数据处理器
        
        Args:
            config_parser: 配置解析器
        """
        self.config_parser = config_parser
        self.logger = logging.getLogger(__name__)
        
    def process_all_strings(self, 
                          all_strings: Dict[str, Dict[str, StringResource]]) -> Dict[str, TranslationData]:
        """
        处理所有字符串数据，整合成统一格式
        
        Args:
            all_strings: 从XML解析器获取的所有字符串数据
        
        Returns:
            整合后的翻译数据字典
        """
        # 获取所有字符串键
        all_keys = self._get_all_string_keys(all_strings)
        
        # 获取所有支持的语言
        supported_languages = set(config.key_col for config in self.config_parser.get_all_configs())
        
        translation_data = {}
        
        for string_key in all_keys:
            translations = {}
            missing_languages = set()
            source_files = {}
            
            # 遍历所有语言配置
            for config in self.config_parser.get_all_configs():
                file_dir = config.file_dir
                lang_code = config.key_col
                
                # 检查该语言是否有这个字符串
                if (file_dir in all_strings and 
                    string_key in all_strings[file_dir]):
                    
                    string_resource = all_strings[file_dir][string_key]
                    translations[lang_code] = string_resource.value
                    source_files[lang_code] = string_resource.file_path
                else:
                    missing_languages.add(lang_code)
            
            # 创建翻译数据对象
            translation_data[string_key] = TranslationData(
                string_key=string_key,
                translations=translations,
                missing_languages=missing_languages,
                source_files=source_files
            )
        
        self.logger.info(f"处理完成，共 {len(translation_data)} 个字符串键")
        self._log_statistics(translation_data)
        
        return translation_data
    
    def _get_all_string_keys(self, all_strings: Dict[str, Dict[str, StringResource]]) -> List[str]:
        """
        获取所有字符串键的并集
        
        Args:
            all_strings: 所有字符串数据
        
        Returns:
            排序后的字符串键列表
        """
        all_keys = set()
        
        for strings in all_strings.values():
            all_keys.update(strings.keys())
        
        return sorted(list(all_keys))
    
    def _log_statistics(self, translation_data: Dict[str, TranslationData]) -> None:
        """
        记录翻译数据统计信息
        
        Args:
            translation_data: 翻译数据
        """
        total_strings = len(translation_data)
        total_languages = len(self.config_parser.get_all_configs())
        
        # 统计完整翻译的字符串数量
        complete_translations = 0
        missing_count_by_language = {}
        
        for lang_config in self.config_parser.get_all_configs():
            missing_count_by_language[lang_config.key_col] = 0
        
        for data in translation_data.values():
            if len(data.missing_languages) == 0:
                complete_translations += 1
            
            for missing_lang in data.missing_languages:
                if missing_lang in missing_count_by_language:
                    missing_count_by_language[missing_lang] += 1
        
        # 记录统计信息
        self.logger.info(f"翻译统计:")
        self.logger.info(f"  总字符串数: {total_strings}")
        self.logger.info(f"  总语言数: {total_languages}")
        self.logger.info(f"  完整翻译的字符串: {complete_translations}")
        self.logger.info(f"  翻译完整度: {complete_translations/total_strings*100:.1f}%")
        
        # 记录每种语言的缺失情况
        for lang_code, missing_count in missing_count_by_language.items():
            if missing_count > 0:
                config = self.config_parser.get_config_by_key_col(lang_code)
                lang_name = config.des if config else lang_code
                completion_rate = (total_strings - missing_count) / total_strings * 100
                self.logger.warning(f"  {lang_name}({lang_code}): 缺失 {missing_count} 个翻译 "
                                  f"(完成度: {completion_rate:.1f}%)")
    
    def filter_translatable_strings(self, 
                                   all_strings: Dict[str, Dict[str, StringResource]],
                                   translation_data: Dict[str, TranslationData]) -> Dict[str, TranslationData]:
        """
        过滤出可翻译的字符串
        
        Args:
            all_strings: 原始字符串数据
            translation_data: 翻译数据
        
        Returns:
            过滤后的翻译数据
        """
        filtered_data = {}
        
        for string_key, data in translation_data.items():
            # 检查是否有任何语言版本标记为可翻译
            is_translatable = False
            
            for file_dir, strings in all_strings.items():
                if string_key in strings:
                    if strings[string_key].translatable:
                        is_translatable = True
                        break
            
            if is_translatable:
                filtered_data[string_key] = data
            else:
                self.logger.debug(f"跳过不可翻译的字符串: {string_key}")
        
        self.logger.info(f"过滤后保留 {len(filtered_data)} 个可翻译字符串")
        return filtered_data
    
    def validate_translations(self, translation_data: Dict[str, TranslationData]) -> List[str]:
        """
        验证翻译数据，检查潜在问题
        
        Args:
            translation_data: 翻译数据
        
        Returns:
            问题报告列表
        """
        issues = []
        
        for string_key, data in translation_data.items():
            # 检查是否有空翻译
            for lang_code, translation in data.translations.items():
                if not translation.strip():
                    config = self.config_parser.get_config_by_key_col(lang_code)
                    lang_name = config.des if config else lang_code
                    issues.append(f"空翻译: {string_key} - {lang_name}({lang_code})")
            
            # 检查是否所有翻译都缺失
            if len(data.translations) == 0:
                issues.append(f"所有语言都缺失翻译: {string_key}")
            
            # 检查翻译长度差异过大的情况
            if len(data.translations) > 1:
                lengths = [len(text) for text in data.translations.values()]
                max_length = max(lengths)
                min_length = min(lengths)
                
                if max_length > 0 and min_length / max_length < 0.3:
                    issues.append(f"翻译长度差异过大: {string_key} "
                                f"(最短: {min_length}, 最长: {max_length})")
        
        if issues:
            self.logger.warning(f"发现 {len(issues)} 个潜在问题")
            for issue in issues[:10]:  # 只记录前10个问题
                self.logger.warning(f"  {issue}")
            if len(issues) > 10:
                self.logger.warning(f"  ... 还有 {len(issues) - 10} 个问题")
        
        return issues
    
    def get_language_order(self) -> List[str]:
        """
        获取语言的显示顺序
        
        Returns:
            语言代码列表，按配置文件顺序排列
        """
        return self.config_parser.get_language_order()
