#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出模块
负责将翻译数据导出为Excel格式，按照指定的样式和布局
"""

import os
import logging
from typing import Dict, List, Optional
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from data_processor import TranslationData
from config_parser import ConfigParser
from utils import ensure_directory_exists


class ExcelExporter:
    """Excel导出器"""
    
    def __init__(self, config_parser: ConfigParser):
        """
        初始化Excel导出器
        
        Args:
            config_parser: 配置解析器
        """
        self.config_parser = config_parser
        self.logger = logging.getLogger(__name__)
        
        # 定义样式
        self._setup_styles()
    
    def _setup_styles(self):
        """设置Excel样式"""
        # 边框样式
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 字体样式
        self.default_font = Font(name='宋体', size=11)
        self.header_font = Font(name='宋体', size=11, color='FFFFFF')
        
        # 填充样式
        self.green_fill = PatternFill(start_color='92D250', end_color='92D250', fill_type='solid')  # ID列
        self.yellow_fill = PatternFill(start_color='F7F94D', end_color='F7F94D', fill_type='solid')  # Key列
        self.blue_fill = PatternFill(start_color='214D79', end_color='214D79', fill_type='solid')    # 语言列
        self.orange_fill = PatternFill(start_color='FEC200', end_color='FEC200', fill_type='solid')  # 代码行
        
        # 对齐样式
        self.left_middle_alignment = Alignment(horizontal='left', vertical='center')
        self.center_alignment = Alignment(horizontal='center', vertical='center')
    
    def export_excel(self, 
                    translation_data: Dict[str, TranslationData],
                    output_path: str,
                    language_order: Optional[List[str]] = None) -> bool:
        """
        导出Excel文件
        
        Args:
            translation_data: 翻译数据
            output_path: 输出文件路径
            language_order: 语言顺序，如果为None则使用配置文件顺序
        
        Returns:
            是否导出成功
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                ensure_directory_exists(output_dir)
            
            # 获取语言顺序
            if language_order is None:
                language_order = self.config_parser.get_language_order()
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "Android多语言"
            
            # 设置列宽
            self._set_column_widths(ws, language_order)
            
            # 写入表头
            self._write_headers(ws, language_order)
            
            # 写入数据
            self._write_data(ws, translation_data, language_order)
            
            # 应用样式
            self._apply_styles(ws, len(translation_data), language_order)
            
            # 保存文件
            wb.save(output_path)
            
            self.logger.info(f"成功导出Excel文件: {output_path}")
            self.logger.info(f"  包含 {len(translation_data)} 个字符串键")
            self.logger.info(f"  包含 {len(language_order)} 种语言")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出Excel文件失败: {e}")
            return False
    
    def _set_column_widths(self, ws, language_order: List[str]):
        """设置列宽"""
        # A列(ID): 104pt ≈ 14
        ws.column_dimensions['A'].width = 14
        
        # B列(模块备注): 104pt ≈ 14  
        ws.column_dimensions['B'].width = 14
        
        # C列(字段key): 104pt ≈ 14
        ws.column_dimensions['C'].width = 14
        
        # 语言列: 156pt ≈ 21
        for i, _ in enumerate(language_order, start=4):
            col_letter = get_column_letter(i)
            ws.column_dimensions[col_letter].width = 21
    
    def _write_headers(self, ws, language_order: List[str]):
        """写入表头"""
        # 第一行：表头
        ws['A1'] = 'id'
        ws['B1'] = '模块备注'
        ws['C1'] = '字段key (string中的)'
        
        # 语言描述
        for i, lang_code in enumerate(language_order, start=4):
            config = self.config_parser.get_config_by_key_col(lang_code)
            lang_name = config.des if config else lang_code
            col_letter = get_column_letter(i)
            ws[f'{col_letter}1'] = lang_name
        
        # 第二行：代码行
        ws['A2'] = 'id'
        ws['B2'] = ''  # 空白
        ws['C2'] = 'textKey'
        
        # 语言代码
        for i, lang_code in enumerate(language_order, start=4):
            col_letter = get_column_letter(i)
            ws[f'{col_letter}2'] = lang_code
    
    def _write_data(self, ws, translation_data: Dict[str, TranslationData], language_order: List[str]):
        """写入翻译数据"""
        # 按字符串键排序
        sorted_keys = sorted(translation_data.keys())
        
        for row_idx, string_key in enumerate(sorted_keys, start=3):
            data = translation_data[string_key]
            
            # A列：空白（可以用作ID）
            ws[f'A{row_idx}'] = ''
            
            # B列：空白（模块备注）
            ws[f'B{row_idx}'] = ''
            
            # C列：字符串键
            ws[f'C{row_idx}'] = string_key
            
            # 语言列：翻译内容
            for i, lang_code in enumerate(language_order, start=4):
                col_letter = get_column_letter(i)
                translation = data.translations.get(lang_code, '')
                ws[f'{col_letter}{row_idx}'] = translation
    
    def _apply_styles(self, ws, data_count: int, language_order: List[str]):
        """应用样式"""
        total_rows = data_count + 2  # 数据行 + 2个表头行
        total_cols = len(language_order) + 3  # 语言列 + 3个固定列
        
        # 应用边框到所有单元格
        for row in range(1, total_rows + 1):
            for col in range(1, total_cols + 1):
                cell = ws.cell(row=row, column=col)
                cell.border = self.thin_border
                cell.font = self.default_font
                cell.alignment = self.left_middle_alignment
        
        # 第一行样式（表头）
        for col in range(1, total_cols + 1):
            cell = ws.cell(row=1, column=col)
            
            if col == 1:  # A列 - ID
                cell.fill = self.green_fill
                cell.font = self.default_font
            elif col == 2:  # B列 - 模块备注
                cell.fill = self.green_fill
                cell.font = self.default_font
            elif col == 3:  # C列 - 字段key
                cell.fill = self.yellow_fill
                cell.font = self.default_font
            else:  # 语言列
                cell.fill = self.blue_fill
                cell.font = self.header_font
        
        # 第二行样式（代码行）
        for col in range(1, total_cols + 1):
            cell = ws.cell(row=2, column=col)
            
            if col == 1:  # A列
                # 保持默认样式
                pass
            elif col == 2:  # B列 - 空白
                # 保持默认样式
                pass
            else:  # C列及语言列
                cell.fill = self.orange_fill
                cell.font = self.default_font
        
        # 数据行样式（从第3行开始）
        for row in range(3, total_rows + 1):
            for col in range(1, total_cols + 1):
                cell = ws.cell(row=row, column=col)
                # 数据行保持默认样式，只有边框和字体
                cell.alignment = self.left_middle_alignment
        
        # 设置行高
        for row in range(1, total_rows + 1):
            ws.row_dimensions[row].height = 16.8

    def export_excel_with_statistics(self,
                                    translation_data: Dict[str, TranslationData],
                                    output_path: str,
                                    language_order: Optional[List[str]] = None) -> bool:
        """
        导出包含统计信息的Excel文件

        Args:
            translation_data: 翻译数据
            output_path: 输出文件路径
            language_order: 语言顺序

        Returns:
            是否导出成功
        """
        try:
            # 先导出基本Excel
            if not self.export_excel(translation_data, output_path, language_order):
                return False

            # 重新打开文件添加统计信息
            from openpyxl import load_workbook
            wb = load_workbook(output_path)

            # 添加统计工作表
            stats_ws = wb.create_sheet("翻译统计")
            self._write_statistics_sheet(stats_ws, translation_data, language_order or self.config_parser.get_language_order())

            # 保存文件
            wb.save(output_path)

            self.logger.info(f"成功添加统计信息到Excel文件: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"添加统计信息失败: {e}")
            return False

    def _write_statistics_sheet(self, ws, translation_data: Dict[str, TranslationData], language_order: List[str]):
        """写入统计信息工作表"""
        # 标题
        ws['A1'] = '翻译统计报告'
        ws['A1'].font = Font(name='宋体', size=14, bold=True)

        # 总体统计
        total_strings = len(translation_data)
        ws['A3'] = '总体统计'
        ws['A3'].font = Font(name='宋体', size=12, bold=True)
        ws['A4'] = f'总字符串数: {total_strings}'
        ws['A5'] = f'总语言数: {len(language_order)}'

        # 各语言统计
        ws['A7'] = '各语言翻译完成情况'
        ws['A7'].font = Font(name='宋体', size=12, bold=True)

        # 表头
        ws['A8'] = '语言'
        ws['B8'] = '语言代码'
        ws['C8'] = '已翻译'
        ws['D8'] = '缺失'
        ws['E8'] = '完成率'

        # 设置表头样式
        for col in ['A', 'B', 'C', 'D', 'E']:
            cell = ws[f'{col}8']
            cell.font = Font(name='宋体', size=11, bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 统计各语言数据
        for i, lang_code in enumerate(language_order, start=9):
            config = self.config_parser.get_config_by_key_col(lang_code)
            lang_name = config.des if config else lang_code

            translated_count = sum(1 for data in translation_data.values()
                                 if lang_code in data.translations and data.translations[lang_code].strip())
            missing_count = total_strings - translated_count
            completion_rate = translated_count / total_strings * 100

            ws[f'A{i}'] = lang_name
            ws[f'B{i}'] = lang_code
            ws[f'C{i}'] = translated_count
            ws[f'D{i}'] = missing_count
            ws[f'E{i}'] = f'{completion_rate:.1f}%'

        # 设置列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 10
        ws.column_dimensions['D'].width = 10
        ws.column_dimensions['E'].width = 10
