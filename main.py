#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android多语言转换工具主程序
将Android strings.xml文件转换为JSON和Excel格式
"""

import os
import sys
import argparse
import logging
from typing import Optional

# 导入自定义模块
from utils import setup_logging, validate_file_exists, get_values_directories
from config_parser import ConfigParser
from xml_parser import XMLParser
from data_processor import DataProcessor
from json_exporter import JSONExporter
from excel_exporter import ExcelExporter


class LanguageConverter:
    """语言转换器主类"""
    
    def __init__(self, base_path: str, config_file: str, output_dir: str):
        """
        初始化转换器
        
        Args:
            base_path: 项目根目录路径
            config_file: 配置文件路径
            output_dir: 输出目录路径
        """
        self.base_path = base_path
        self.config_file = config_file
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个组件
        self.config_parser = None
        self.xml_parser = None
        self.data_processor = None
        self.json_exporter = None
        self.excel_exporter = None
    
    def initialize(self) -> bool:
        """
        初始化所有组件
        
        Returns:
            是否初始化成功
        """
        try:
            # 验证输入路径
            if not os.path.exists(self.base_path):
                self.logger.error(f"项目根目录不存在: {self.base_path}")
                return False
            
            if not validate_file_exists(self.config_file):
                self.logger.error(f"配置文件不存在: {self.config_file}")
                return False
            
            # 创建输出目录
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
                self.logger.info(f"创建输出目录: {self.output_dir}")
            
            # 初始化配置解析器
            self.config_parser = ConfigParser(self.config_file)
            if not self.config_parser.load_config():
                self.logger.error("配置文件加载失败")
                return False
            
            # 初始化其他组件
            self.xml_parser = XMLParser(self.base_path)
            self.data_processor = DataProcessor(self.config_parser)
            self.json_exporter = JSONExporter(self.config_parser)
            self.excel_exporter = ExcelExporter(self.config_parser)
            
            self.logger.info("所有组件初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    def convert(self, export_json: bool = True, export_excel: bool = True, 
                include_non_translatable: bool = False) -> bool:
        """
        执行转换过程
        
        Args:
            export_json: 是否导出JSON
            export_excel: 是否导出Excel
            include_non_translatable: 是否包含不可翻译的字符串
        
        Returns:
            是否转换成功
        """
        try:
            self.logger.info("开始转换过程...")
            
            # 第一步：解析所有XML文件
            self.logger.info("步骤1: 解析XML文件...")
            all_strings = self.xml_parser.parse_all_values_directories()
            
            if not all_strings:
                self.logger.error("没有找到任何strings.xml文件")
                return False
            
            # 第二步：处理和整合数据
            self.logger.info("步骤2: 处理和整合数据...")
            translation_data = self.data_processor.process_all_strings(all_strings)
            
            if not translation_data:
                self.logger.error("没有找到任何翻译数据")
                return False
            
            # 第三步：过滤数据（可选）
            if not include_non_translatable:
                self.logger.info("步骤3: 过滤不可翻译的字符串...")
                translation_data = self.data_processor.filter_translatable_strings(
                    all_strings, translation_data)
            
            # 第四步：验证数据
            self.logger.info("步骤4: 验证翻译数据...")
            issues = self.data_processor.validate_translations(translation_data)
            if issues:
                self.logger.warning(f"发现 {len(issues)} 个数据问题，但继续处理")
            
            # 第五步：导出JSON（可选）
            if export_json:
                self.logger.info("步骤5: 导出JSON文件...")
                if not self._export_json_files(translation_data):
                    self.logger.error("JSON导出失败")
                    return False
            
            # 第六步：导出Excel（可选）
            if export_excel:
                self.logger.info("步骤6: 导出Excel文件...")
                if not self._export_excel_files(translation_data):
                    self.logger.error("Excel导出失败")
                    return False
            
            self.logger.info("转换过程完成！")
            self._print_summary(translation_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"转换过程中发生错误: {e}")
            return False
    
    def _export_json_files(self, translation_data) -> bool:
        """导出JSON文件"""
        try:
            json_dir = os.path.join(self.output_dir, "json")
            
            # 导出合并JSON
            merged_path = os.path.join(json_dir, "translations_merged.json")
            if not self.json_exporter.export_merged_json(translation_data, merged_path):
                return False
            
            # 导出分离JSON
            separated_dir = os.path.join(json_dir, "separated")
            if not self.json_exporter.export_separated_json(translation_data, separated_dir):
                return False
            
            # 导出摘要JSON
            summary_path = os.path.join(json_dir, "translation_summary.json")
            if not self.json_exporter.export_translation_summary(translation_data, summary_path):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"JSON导出失败: {e}")
            return False
    
    def _export_excel_files(self, translation_data) -> bool:
        """导出Excel文件"""
        try:
            excel_dir = os.path.join(self.output_dir, "excel")
            
            # 导出基本Excel
            excel_path = os.path.join(excel_dir, "translations.xlsx")
            if not self.excel_exporter.export_excel(translation_data, excel_path):
                return False
            
            # 导出包含统计信息的Excel
            stats_excel_path = os.path.join(excel_dir, "translations_with_stats.xlsx")
            if not self.excel_exporter.export_excel_with_statistics(translation_data, stats_excel_path):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Excel导出失败: {e}")
            return False
    
    def _print_summary(self, translation_data):
        """打印转换摘要"""
        total_strings = len(translation_data)
        total_languages = len(self.config_parser.get_all_configs())
        
        print("\n" + "="*60)
        print("转换完成摘要")
        print("="*60)
        print(f"总字符串数: {total_strings}")
        print(f"总语言数: {total_languages}")
        print(f"输出目录: {self.output_dir}")
        
        # 统计各语言完成度
        language_order = self.data_processor.get_language_order()
        print("\n各语言翻译完成度:")
        for lang_code in language_order:
            config = self.config_parser.get_config_by_key_col(lang_code)
            lang_name = config.des if config else lang_code
            
            translated_count = sum(1 for data in translation_data.values() 
                                 if lang_code in data.translations and data.translations[lang_code].strip())
            completion_rate = translated_count / total_strings * 100
            
            print(f"  {lang_name}({lang_code}): {completion_rate:.1f}% ({translated_count}/{total_strings})")
        
        print("="*60)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Android多语言转换工具 - 将strings.xml转换为JSON和Excel格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                                    # 使用默认设置
  python main.py --input-dir /path/to/project       # 指定输入目录
  python main.py --output-dir /path/to/output       # 指定输出目录
  python main.py --config-file config.json          # 指定配置文件
  python main.py --json-only                        # 只导出JSON
  python main.py --excel-only                       # 只导出Excel
  python main.py --include-non-translatable         # 包含不可翻译字符串
  python main.py --log-level DEBUG                  # 设置日志级别
        """
    )

    parser.add_argument(
        '--input-dir', '-i',
        default='.',
        help='输入目录路径（包含values文件夹的项目根目录，默认: 当前目录）'
    )

    parser.add_argument(
        '--output-dir', '-o',
        default='./output',
        help='输出目录路径（默认: ./output）'
    )

    parser.add_argument(
        '--config-file', '-c',
        default='./language_config.json',
        help='语言配置文件路径（默认: ./language_config.json）'
    )

    parser.add_argument(
        '--json-only',
        action='store_true',
        help='只导出JSON格式'
    )

    parser.add_argument(
        '--excel-only',
        action='store_true',
        help='只导出Excel格式'
    )

    parser.add_argument(
        '--include-non-translatable',
        action='store_true',
        help='包含标记为不可翻译的字符串'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别（默认: INFO）'
    )

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 设置日志
    logger = setup_logging(args.log_level)

    # 打印启动信息
    print("Android多语言转换工具")
    print("="*60)
    print(f"输入目录: {args.input_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"配置文件: {args.config_file}")
    print(f"日志级别: {args.log_level}")
    print("="*60)

    try:
        # 创建转换器
        converter = LanguageConverter(
            base_path=args.input_dir,
            config_file=args.config_file,
            output_dir=args.output_dir
        )

        # 初始化
        if not converter.initialize():
            logger.error("转换器初始化失败")
            sys.exit(1)

        # 确定导出格式
        export_json = not args.excel_only
        export_excel = not args.json_only

        # 执行转换
        success = converter.convert(
            export_json=export_json,
            export_excel=export_excel,
            include_non_translatable=args.include_non_translatable
        )

        if success:
            print("\n✅ 转换成功完成！")
            sys.exit(0)
        else:
            print("\n❌ 转换失败！")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
