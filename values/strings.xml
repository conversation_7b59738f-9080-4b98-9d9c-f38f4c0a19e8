<resources>
    <!--    <string name="app_name" translatable="false">&#160;&#160;Bible</string>-->
    <string name="app_name" translatable="false">@string/app_name2</string>
    <string name="app_name2" translatable="false">@string/derived_app_name</string>
    <!--基础配置-->
    <string name="abc_menu_tab_shortcut_label" translatable="false">config/config.json</string>

    <string name="language_1">English</string>
    <string name="language_2">हिंदी</string>
    <string name="language_3">Português</string>
    <string name="language_4">Русский</string>
    <string name="language_5">bahasa Indonesia</string>
    <string name="language_6">español</string>
    <string name="language_7">한국인</string>
    <string name="language_8">Deutsch</string>
    <string name="language_9">Italiano</string>
    <string name="language_10">Français</string>
    <string name="language_11">日本</string>
    <string name="language_12">Filipino</string>
    <string name="first_to_splash">This action may contain advertising</string>
    <string name="language_title">Languages</string>
    <string name="guide11_title">Record your health data</string>
    <string name="guide11_text">Easily record heart rate, blood pressure, blood sugar, BMI health data.</string>
    <string name="guide2_title">Analyze your health</string>
    <string name="guide2_text">Get a clearer understanding of your health status and changes through charts and graphs.</string>
    <string name="guide3_title">Disclaimer</string>
    <string name="guide3_text">This application is not suitable for use in medical emergencies. If you need any assistance, please consult your doctor.</string>
    <string name="next_botton">Next</string>
    <string name="end_botton">Get Started</string>
    <string name="language_set">Languages</string>
    <string name="agreement">Privacy Policy</string>
    <string name="privacy_policy">User Terms</string>
    <string name="tracker">Tracker</string>
    <string name="heart_title">Heart Rate</string>
    <string name="heart_button">Measure</string>
    <string name="pressure_title">Blood Pressure</string>
    <string name="pressure_button">Add</string>
    <string name="sugar_title">Blood Sugar</string>
    <string name="analysis_button">GO</string>
    <string name="analysis_title">Health Analysis</string>
    <string name="articles_title">Health Articles</string>
    <string name="tab1">Home</string>
    <string name="tab2">Analysis</string>
    <string name="tab3">Health info</string>
    <string name="tab4">Settings</string>
    <string name="prem_title">Heart Rate</string>
    <string name="prem_tips">In order to achieve heart rate measurement, please allow us to access the camera, which will not result in your privacy being compromised. Please use it with confidence!</string>
    <string name="prem_but1">Cancel</string>
    <string name="prem_but2">Allow</string>
    <string name="helppage_title">Heart Rate</string>
    <string name="help_title">How to measure heart rate</string>
    <string name="help_text1">Cover both the phone camera and flash with your palm until the measurement is complete.</string>
    <string name="help_text2">If the measurement fails even after covering it at the same time, we apologize that your device is not compatible with this feature.</string>
    <string name="help_but">To measure</string>
    <string name="heart_mer_title">Heart Rate</string>
    <string name="heart_mer_last">Last measurement</string>
    <string name="heart_mer_tips1">Cover the camera with your fingers for a while</string>
    <string name="heart_mer_tips2">Please cover the camera with your finger</string>
    <string name="heart_status">Status</string>
    <string name="heart_status1">Resting</string>
    <string name="heart_status2">Sitting</string>
    <string name="heart_status3">Standing</string>
    <string name="heart_status4">Lying</string>
    <string name="heart_status5">Meditation</string>
    <string name="heart_status6">Exercise</string>
    <string name="heart_status7">Walking</string>
    <string name="heart_status8">Running</string>
    <string name="heart_status9">Swimming</string>
    <string name="heart_status10">Jumping</string>
    <string name="heart_gender">Gender</string>
    <string name="heart_gender1">Male</string>
    <string name="heart_gender2">Female</string>
    <string name="heart_gender3">Other</string>
    <string name="heart_age">Age</string>
    <string name="heart_ageno">Unknown</string>
    <string name="heart_mer_save">Save</string>
    <string name="heart_result_title">Result</string>
    <string name="heart_result_tips">The result of your heart rate level is</string>
    <string name="heart_result1">Slow</string>
    <string name="heart_result2">Normal</string>
    <string name="heart_result3">Fast</string>
    <string name="heart_result4">Warm up</string>
    <string name="heart_result5">Target HR training</string>
    <string name="heart_result6">High intensity</string>
    <string name="heart_result7">Extreme</string>
    <string name="heart_trend">Trend</string>
    <string name="heart_unlock_title">Watch an ad to unlock</string>
    <string name="heart_unlock_but">Unlock</string>
    <string name="heart_average">Average</string>
    <string name="heart_max">Max</string>
    <string name="heart_min">Min</string>
    <string name="heart_overview">Overview</string>
    <string name="heart_overview1">Relaxed state</string>
    <string name="heart_overview2">Active state</string>
    <string name="heart_more">More</string>
    <string name="heart_history">History</string>
    <string name="pressure_date">Date</string>
    <string name="systolic">Systolic</string>
    <string name="diastolic">Diastolic</string>
    <string name="pulse">Pulse</string>
    <string name="pressure_result1">Hypotension</string>
    <string name="pressure_result2">Normal</string>
    <string name="pressure_result3">Elevated</string>
    <string name="pressure_result4">Hypertension·Stage 1</string>
    <string name="pressure_result5">Hypertension·Stage 2</string>
    <string name="pressure_result6">Hypertensive</string>
    <string name="pressure_save">Save</string>
    <string name="pressure_result_title">Result</string>
    <string name="pressure_result_tips">The result of your blood pressure level is</string>
    <string name="pressure_trend">Trend</string>
    <string name="pressure_unlock_title">Watch an ad to unlock</string>
    <string name="pressure_unlock_but">Unlock</string>
    <string name="pressure_overview">Overview</string>
    <string name="pressure_more">More</string>
    <string name="sugar_status">Status</string>
    <string name="sugar_status1">Default</string>
    <string name="sugar_status2">Before exercise</string>
    <string name="sugar_status3">Before a meal</string>
    <string name="sugar_status4">Fasting</string>
    <string name="sugar_status5">After a meal（1h）</string>
    <string name="sugar_status6">After a meal（2h）</string>
    <string name="sugar_status7">After exercise</string>
    <string name="sugar_status8">Asleep</string>
    <string name="sugar_date">Date</string>
    <string name="sugar_result1">Low</string>
    <string name="sugar_result2">Normal</string>
    <string name="sugar_result3">Pre-diabetes</string>
    <string name="sugar_result4">Diabetes</string>
    <string name="sugar_save">Save</string>
    <string name="sugar_result_title">Result</string>
    <string name="sugar_result_tips">The result of your blood sugar level is</string>
    <string name="sugar_trend">Trend</string>
    <string name="sugar_unlock_title">Watch an ad to unlock</string>
    <string name="sugar_unlock_but">Unlock</string>
    <string name="sugar_overview">Overview</string>
    <string name="sugar_more">More</string>
    <string name="heart_analysis">Heart Rate</string>
    <string name="pressure_analysis">Blood Sugar</string>
    <string name="unlock_tips">Watch an ad to unlock</string>
    <string name="unlock_but">Unlock</string>
    <string name="record_tips">Record your first data now！</string>
    <string name="record_but">Add</string>
    <string name="info_tab1">Heart Rate</string>
    <string name="info_tab2">Blood Pressure</string>
    <string name="info_tab3">Blood Sugar</string>
    <string name="set_title">Settings</string>
    <string name="set_help">How to measure heart rate</string>
    <string name="heart_start">Start</string>
    <string name="heart_record">Save Record</string>
    <string name="heart_record_status">Status:</string>
    <string name="heart_record_gender">Gender:</string>
    <string name="heart_record_age">Age:</string>
    <string name="heart_total">Total</string>
    <string name="health_banner_1">Health &amp;Dietary \nTips</string>
    <string name="health_banner_2">Daily Health \nNews</string>
    <string name="guide1_title">Focus on health</string>
    <string name="guide1_text">Regularly record and keep track of your health.</string>
    <string name="loading_failed">Loading failed, please check the network and try again</string>
    <string name="try_again">Please try again</string>
    <string name="blood_pressure_range1">SYS &lt; 90 or DIA &lt; 60</string>
    <string name="blood_pressure_range2">SYS 90-119 and DlA 60-79</string>
    <string name="blood_pressure_range3">SYS 90-119 and DlA 60-79</string>
    <string name="blood_pressure_range4">SYS 130-139 or DIA 80-89</string>
    <string name="blood_pressure_range5">SYS 140-180 0r DIA 90-120</string>
    <string name="blood_pressure_range6">SYS>180 0r DIA>120</string>

    <string name="terms">Terms of Service</string>
    <string name="brvah_in_loading">Loading...</string>

    <string name="push_btn_measure">Measure</string>
    <string name="push_btn_view">View</string>
    <string name="push_txt_news">Daily health news has been updated! To learn about the latest medical and health information.</string>
    <string name="push_txt_eat">What\'s good to eat today? Take a look at healthy eating tips as a reference!</string>
    <string name="push_txt_exercise">Monitor heart rate during exercise to avoid danger!</string>
    <string name="push_txt_lose_weight">The formula for calculating weight loss heart rate is "(220-age) x 0.6 to (220-age) x 0.8". Have you reached your weight loss heart rate?</string>
    <string name="push_txt_analyze">Only one measurement value is difficult to better analyze your health. It is recommended that you record more times to better detect changes in your health.</string>
    <string name="push_txt_night">Measuring heart rate before resting at night can help understand the changes in heart rate after the end of the day\'s activities.</string>
    <string name="push_txt_morning">Waking up is an ideal time to measure heart rate, which helps to understand the body\'s heart rate in a completely relaxed state.</string>

    <string name="push_btn_check">Check out</string>
    <string name="push_txt_short_play">The popular short dramas have been updated! </string>
    <string name="push_txt_weather_morning">Good morning!Click to check weather ...</string>
    <string name="push_txt_weather_night">Good evening!Click to check weather...</string>
    <string name="push_txt_weather_storm1">There is a possibility of a storm happening near...</string>
    <string name="push_txt_weather_storm2">Tropical storms may occur in the near future. click ...</string>
    <string name="push_btn_big_check">Check out Now</string>

    <string name="push_btn_full_view">Read More</string>
    <string name="push_txt_night1">Measuring heart rate before resting at night\n</string>
    <string name="push_txt_night2">Can help understand the changes in heart rate after the end of the day\'s activities.</string>
    <string name="push_txt_morning1">Waking up is an ideal time to measure heart rate,\n </string>
    <string name="push_txt_morning2">Which helps to understand the body\'s heart rate in a completely relaxed state.</string>
    <string name="push_txt_analyze1">Only one measurement value is difficult to better analyze your health.\n</string>
    <string name="push_txt_analyze2">It is recommended that you record more times to better detect changes in your health.</string>
    <string name="go_to_set">Go to set</string>
    <string name="permission_txt1">Authorize overlay permission to</string>
    <string name="permission_txt2">better remind you to record heart rate and protect your health.</string>
    <string name="skip">Skip</string>
    <string name="push_heart_fail">Suspected abnormal heart rate detected, go and measure your heart rate quickly!</string>

    <string name="health_analysis">Health Analysis</string>
    <string name="health_rate_analysis">Heart Rate Analysis</string>
    <string name="blood_pressure_analysis">Blood Pressure Analysis</string>
    <string name="push_blood_btn">Record</string>
    <string name="push_blood_content">Continuously recording blood pressure data for 7 days can help detect health problems in a timely manner!</string>

    <string name="evaluate_title">Evaluate</string>
    <string name="evaluate_msg">How do you evaluate us?</string>
    <string name="evaluate_content1">Sorry for any inconvenience caused</string>
    <string name="evaluate_content2">We\'re glad you love Daily HeartTrack Launcher！ Thank you！</string>
    <string name="rate_hint">Write down your displeasure – we’ll address it immediately.</string>
    <string name="rate_commit">Feedback</string>
    <string name="rate_commit_5">Evaluate us</string>
    <string name="rate_commit_feedback">Feedback success</string>

    <string name="faq">FAQ</string>
    <string name="reset_homescreen">After installing Launcher, your home screen will change, making it easier for you to access the features. The home screen will display commonly used apps such as phone, SMS, camera, etc. If you want to place other apps on the home screen, simply swipe up on the home screen to view all apps, and drag the apps you want to the home screen. If you are not satisfied with the new home screen, you can switch back to the original home screen, but please note that when you are no longer using Launcher, you will no longer be able to use the features. Click the button below to change the default launcher.</string>

</resources>
