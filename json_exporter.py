#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON导出模块
负责将翻译数据导出为JSON格式，支持合并格式和分离格式
"""

import json
import os
import logging
from typing import Dict, List, Optional
from data_processor import TranslationData
from config_parser import ConfigParser
from utils import ensure_directory_exists


class JSONExporter:
    """JSON导出器"""
    
    def __init__(self, config_parser: ConfigParser):
        """
        初始化JSON导出器
        
        Args:
            config_parser: 配置解析器
        """
        self.config_parser = config_parser
        self.logger = logging.getLogger(__name__)
    
    def export_merged_json(self, 
                          translation_data: Dict[str, TranslationData],
                          output_path: str,
                          language_order: Optional[List[str]] = None) -> bool:
        """
        导出合并的JSON文件
        格式: {"string_key": {"lang_code": "translation", ...}, ...}
        
        Args:
            translation_data: 翻译数据
            output_path: 输出文件路径
            language_order: 语言顺序，如果为None则使用配置文件顺序
        
        Returns:
            是否导出成功
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                ensure_directory_exists(output_dir)
            
            # 获取语言顺序
            if language_order is None:
                language_order = self.config_parser.get_language_order()
            
            # 构建合并的JSON数据
            merged_data = {}
            
            for string_key, data in translation_data.items():
                # 按指定顺序排列语言
                ordered_translations = {}
                for lang_code in language_order:
                    if lang_code in data.translations:
                        ordered_translations[lang_code] = data.translations[lang_code]
                
                # 添加其他未在顺序中的语言
                for lang_code, translation in data.translations.items():
                    if lang_code not in ordered_translations:
                        ordered_translations[lang_code] = translation
                
                merged_data[string_key] = ordered_translations
            
            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=2, sort_keys=True)
            
            self.logger.info(f"成功导出合并JSON文件: {output_path}")
            self.logger.info(f"  包含 {len(merged_data)} 个字符串键")
            self.logger.info(f"  包含 {len(language_order)} 种语言")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出合并JSON文件失败: {e}")
            return False
    
    def export_separated_json(self, 
                             translation_data: Dict[str, TranslationData],
                             output_dir: str) -> bool:
        """
        导出分离的JSON文件（每种语言一个文件）
        格式: {"string_key": "translation", ...}
        
        Args:
            translation_data: 翻译数据
            output_dir: 输出目录
        
        Returns:
            是否导出成功
        """
        try:
            # 确保输出目录存在
            ensure_directory_exists(output_dir)
            
            # 获取所有语言
            all_languages = set()
            for data in translation_data.values():
                all_languages.update(data.translations.keys())
            
            success_count = 0
            
            # 为每种语言生成单独的JSON文件
            for lang_code in all_languages:
                config = self.config_parser.get_config_by_key_col(lang_code)
                lang_name = config.des if config else lang_code
                
                # 构建该语言的数据
                lang_data = {}
                for string_key, data in translation_data.items():
                    if lang_code in data.translations:
                        lang_data[string_key] = data.translations[lang_code]
                
                # 生成文件名
                filename = f"strings_{lang_code}.json"
                file_path = os.path.join(output_dir, filename)
                
                # 写入JSON文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(lang_data, f, ensure_ascii=False, indent=2, sort_keys=True)
                
                self.logger.info(f"导出 {lang_name}({lang_code}): {file_path} "
                               f"({len(lang_data)} 个字符串)")
                success_count += 1
            
            self.logger.info(f"成功导出 {success_count} 个分离JSON文件到: {output_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出分离JSON文件失败: {e}")
            return False
    
    def export_translation_summary(self, 
                                  translation_data: Dict[str, TranslationData],
                                  output_path: str) -> bool:
        """
        导出翻译摘要JSON文件，包含统计信息
        
        Args:
            translation_data: 翻译数据
            output_path: 输出文件路径
        
        Returns:
            是否导出成功
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                ensure_directory_exists(output_dir)
            
            # 计算统计信息
            total_strings = len(translation_data)
            all_languages = set()
            language_stats = {}
            
            for data in translation_data.values():
                all_languages.update(data.translations.keys())
            
            # 统计每种语言的翻译情况
            for lang_code in all_languages:
                config = self.config_parser.get_config_by_key_col(lang_code)
                lang_name = config.des if config else lang_code
                
                translated_count = sum(1 for data in translation_data.values() 
                                     if lang_code in data.translations)
                missing_count = total_strings - translated_count
                completion_rate = translated_count / total_strings * 100
                
                language_stats[lang_code] = {
                    "language_name": lang_name,
                    "language_code": lang_code,
                    "translated_count": translated_count,
                    "missing_count": missing_count,
                    "completion_rate": round(completion_rate, 2)
                }
            
            # 构建摘要数据
            summary_data = {
                "summary": {
                    "total_strings": total_strings,
                    "total_languages": len(all_languages),
                    "generated_at": self._get_current_timestamp()
                },
                "language_statistics": language_stats,
                "missing_translations": self._get_missing_translations(translation_data)
            }
            
            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功导出翻译摘要: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出翻译摘要失败: {e}")
            return False
    
    def _get_missing_translations(self, translation_data: Dict[str, TranslationData]) -> Dict[str, List[str]]:
        """
        获取缺失翻译的详细信息
        
        Args:
            translation_data: 翻译数据
        
        Returns:
            缺失翻译的字典，键为语言代码，值为缺失的字符串键列表
        """
        missing_by_language = {}
        
        for string_key, data in translation_data.items():
            for missing_lang in data.missing_languages:
                if missing_lang not in missing_by_language:
                    missing_by_language[missing_lang] = []
                missing_by_language[missing_lang].append(string_key)
        
        # 对每种语言的缺失列表进行排序
        for lang_code in missing_by_language:
            missing_by_language[lang_code].sort()
        
        return missing_by_language
    
    def _get_current_timestamp(self) -> str:
        """
        获取当前时间戳
        
        Returns:
            ISO格式的时间戳字符串
        """
        from datetime import datetime
        return datetime.now().isoformat()
    
    def export_all_formats(self, 
                          translation_data: Dict[str, TranslationData],
                          output_dir: str,
                          base_filename: str = "translations") -> bool:
        """
        导出所有JSON格式
        
        Args:
            translation_data: 翻译数据
            output_dir: 输出目录
            base_filename: 基础文件名
        
        Returns:
            是否全部导出成功
        """
        success = True
        
        # 导出合并JSON
        merged_path = os.path.join(output_dir, f"{base_filename}_merged.json")
        if not self.export_merged_json(translation_data, merged_path):
            success = False
        
        # 导出分离JSON
        separated_dir = os.path.join(output_dir, "separated")
        if not self.export_separated_json(translation_data, separated_dir):
            success = False
        
        # 导出摘要JSON
        summary_path = os.path.join(output_dir, f"{base_filename}_summary.json")
        if not self.export_translation_summary(translation_data, summary_path):
            success = False
        
        return success
