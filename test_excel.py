#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel文件内容的脚本
"""

import openpyxl
from openpyxl.utils import get_column_letter

def test_excel_file(file_path):
    """测试Excel文件内容"""
    print(f"测试Excel文件: {file_path}")
    print("="*60)
    
    try:
        # 打开Excel文件
        wb = openpyxl.load_workbook(file_path)
        
        # 获取主工作表
        ws = wb.active
        print(f"工作表名称: {ws.title}")
        
        # 检查表头（第一行）
        print("\n第一行（语言描述）:")
        for col in range(1, 15):  # 检查前14列
            cell_value = ws.cell(row=1, column=col).value
            col_letter = get_column_letter(col)
            print(f"  {col_letter}1: {cell_value}")
        
        # 检查第二行（语言代码）
        print("\n第二行（语言代码）:")
        for col in range(1, 15):  # 检查前14列
            cell_value = ws.cell(row=2, column=col).value
            col_letter = get_column_letter(col)
            print(f"  {col_letter}2: {cell_value}")
        
        # 检查第三行（第一个数据行）
        print("\n第三行（第一个数据行）:")
        for col in range(1, 8):  # 检查前7列
            cell_value = ws.cell(row=3, column=col).value
            col_letter = get_column_letter(col)
            print(f"  {col_letter}3: {cell_value}")
        
        # 检查总行数和列数
        max_row = ws.max_row
        max_col = ws.max_column
        print(f"\n文件统计:")
        print(f"  总行数: {max_row}")
        print(f"  总列数: {max_col}")
        print(f"  数据行数: {max_row - 2}")  # 减去两个表头行
        
        # 检查样式
        print(f"\n样式检查:")
        # 检查A1单元格（绿色）
        a1_fill = ws['A1'].fill
        print(f"  A1填充色: {a1_fill.start_color.rgb if a1_fill.start_color else 'None'}")
        
        # 检查C1单元格（黄色）
        c1_fill = ws['C1'].fill
        print(f"  C1填充色: {c1_fill.start_color.rgb if c1_fill.start_color else 'None'}")
        
        # 检查D1单元格（蓝色）
        d1_fill = ws['D1'].fill
        print(f"  D1填充色: {d1_fill.start_color.rgb if d1_fill.start_color else 'None'}")
        
        wb.close()
        print("\n✅ Excel文件测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_excel_file("output/excel/translations.xlsx")
