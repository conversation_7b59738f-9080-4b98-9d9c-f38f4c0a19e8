#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XML解析模块
负责解析Android strings.xml文件，提取字符串资源，正确处理XML转义
"""

import os
import logging
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from utils import validate_file_exists, unescape_xml_entities, get_values_directories


@dataclass
class StringResource:
    """字符串资源数据类"""
    name: str           # 字符串名称/键
    value: str          # 字符串值
    translatable: bool  # 是否可翻译
    file_path: str      # 来源文件路径


class XMLParser:
    """XML解析器"""
    
    def __init__(self, base_path: str):
        """
        初始化XML解析器
        
        Args:
            base_path: 项目根目录路径
        """
        self.base_path = base_path
        self.logger = logging.getLogger(__name__)
        
    def parse_strings_xml(self, file_path: str) -> Dict[str, StringResource]:
        """
        解析单个strings.xml文件
        
        Args:
            file_path: strings.xml文件路径
        
        Returns:
            字符串资源字典，键为字符串名称，值为StringResource对象
        """
        strings = {}
        
        try:
            if not validate_file_exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                return strings
            
            # 解析XML文件
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            if root.tag != 'resources':
                self.logger.warning(f"XML根标签不是'resources': {file_path}")
                return strings
            
            # 遍历所有string标签
            for string_elem in root.findall('string'):
                name = string_elem.get('name')
                if not name:
                    self.logger.warning(f"发现没有name属性的string标签: {file_path}")
                    continue
                
                # 获取translatable属性，默认为True
                translatable_attr = string_elem.get('translatable', 'true').lower()
                translatable = translatable_attr != 'false'
                
                # 获取字符串值
                value = self._extract_string_value(string_elem)
                
                # 创建字符串资源对象
                string_resource = StringResource(
                    name=name,
                    value=value,
                    translatable=translatable,
                    file_path=file_path
                )
                
                strings[name] = string_resource
                
            self.logger.debug(f"从 {file_path} 解析出 {len(strings)} 个字符串")
            
        except ET.ParseError as e:
            self.logger.error(f"XML解析错误 {file_path}: {e}")
        except Exception as e:
            self.logger.error(f"解析文件时发生错误 {file_path}: {e}")
        
        return strings
    
    def _extract_string_value(self, string_elem: ET.Element) -> str:
        """
        提取string元素的值，正确处理XML转义和格式
        
        Args:
            string_elem: string XML元素
        
        Returns:
            处理后的字符串值
        """
        # 获取元素的文本内容
        text = string_elem.text or ''
        
        # 处理子元素（如<b>, <i>等格式标签）
        if len(string_elem) > 0:
            # 如果有子元素，需要重新构建完整的文本
            text = self._reconstruct_text_with_tags(string_elem)
        
        # 反转义XML实体
        text = unescape_xml_entities(text)
        
        # 处理Android特有的转义
        text = self._handle_android_escapes(text)
        
        return text.strip()
    
    def _reconstruct_text_with_tags(self, elem: ET.Element) -> str:
        """
        重新构建包含子标签的文本内容
        
        Args:
            elem: XML元素
        
        Returns:
            重构的文本
        """
        result = elem.text or ''
        
        for child in elem:
            # 添加子元素的开始标签
            result += f'<{child.tag}'
            if child.attrib:
                for key, value in child.attrib.items():
                    result += f' {key}="{value}"'
            result += '>'
            
            # 添加子元素的文本内容
            result += child.text or ''
            
            # 递归处理子元素的子元素
            if len(child) > 0:
                result += self._reconstruct_text_with_tags(child)
            
            # 添加子元素的结束标签
            result += f'</{child.tag}>'
            
            # 添加子元素后的文本
            result += child.tail or ''
        
        return result
    
    def _handle_android_escapes(self, text: str) -> str:
        """
        处理Android特有的转义字符
        
        Args:
            text: 原始文本
        
        Returns:
            处理后的文本
        """
        if not text:
            return text
        
        # Android中的转义处理
        # 注意：我们要保持原始格式，不进行额外的转义
        result = text
        
        # 处理反斜杠转义
        result = result.replace('\\n', '\n')    # 换行符
        result = result.replace('\\t', '\t')    # 制表符
        result = result.replace('\\r', '\r')    # 回车符
        result = result.replace('\\"', '"')     # 双引号
        result = result.replace("\\'", "'")     # 单引号
        result = result.replace('\\\\', '\\')   # 反斜杠（最后处理）
        
        return result
    
    def parse_all_values_directories(self) -> Dict[str, Dict[str, StringResource]]:
        """
        解析所有values目录中的strings.xml文件
        
        Returns:
            字典，键为目录名，值为该目录下的字符串资源字典
        """
        all_strings = {}
        
        # 获取所有values目录
        values_dirs = get_values_directories(self.base_path)
        
        for values_dir in values_dirs:
            strings_xml_path = os.path.join(self.base_path, values_dir, 'strings.xml')
            
            if validate_file_exists(strings_xml_path):
                strings = self.parse_strings_xml(strings_xml_path)
                all_strings[values_dir] = strings
                self.logger.info(f"解析 {values_dir}: {len(strings)} 个字符串")
            else:
                self.logger.warning(f"strings.xml不存在: {strings_xml_path}")
                all_strings[values_dir] = {}
        
        return all_strings
    
    def get_all_string_keys(self, all_strings: Dict[str, Dict[str, StringResource]]) -> List[str]:
        """
        获取所有字符串键的并集
        
        Args:
            all_strings: 所有字符串资源
        
        Returns:
            所有字符串键的列表
        """
        all_keys = set()
        
        for strings in all_strings.values():
            all_keys.update(strings.keys())
        
        return sorted(list(all_keys))
